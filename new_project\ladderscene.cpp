#include "ladderscene.h"
#include <QPainter>
#include <QGraphicsSceneMouseEvent>
#include <QDebug>
#include <qmath.h>

LadderScene::LadderScene(QObject *parent)
    : QGraphicsScene(parent)
    , m_ladderData(nullptr)
    , m_startRow(0)
    , m_startCol(0)
    , m_visibleRows(CELL_PER_COL)
    , m_visibleCols(CELL_PER_ROW)
    , m_selectedRow(-1)
    , m_selectedCol(-1)
    , m_debugMode(false)
    , m_editMode(false)
    , m_forceRefresh(false)
    , m_normalMetrics(nullptr)
    , m_boldMetrics(nullptr)
    , m_smallMetrics(nullptr)
    , m_needsFullRedraw(true)
{
    setupScene();
    setupFonts();
}

LadderScene::~LadderScene()
{
    delete m_normalMetrics;
    delete m_boldMetrics;
    delete m_smallMetrics;
}

void LadderScene::setupScene()
{
    // 设置场景背景
    m_backgroundColor = QColor(255, 255, 255);
    m_gridColor = QColor(200, 200, 200);
    m_textColor = QColor(0, 0, 0);
    m_borderColor = QColor(100, 100, 100);
    m_selectionColor = QColor(0, 0, 255, 100);
    
    setBackgroundBrush(QBrush(m_backgroundColor));
}

void LadderScene::setupFonts()
{
    // 设置字体
    m_normalFont = QFont("Arial", 9);
    m_boldFont = QFont("Arial", 9, QFont::Bold);
    m_smallFont = QFont("Arial", 8);
    
    // 创建字体度量
    m_normalMetrics = new QFontMetrics(m_normalFont);
    m_boldMetrics = new QFontMetrics(m_boldFont);
    m_smallMetrics = new QFontMetrics(m_smallFont);
}

void LadderScene::setLadderData(LadderData *data)
{
    if (m_ladderData) {
        disconnect(m_ladderData, nullptr, this, nullptr);
    }
    
    m_ladderData = data;
    
    if (m_ladderData) {
        connect(m_ladderData, &LadderData::dataChanged,
                this, &LadderScene::onDataChanged);
        connect(m_ladderData, &LadderData::rowDataChanged,
                this, &LadderScene::onRowDataChanged);
    }
    
    m_needsFullRedraw = true;
    update();
}

void LadderScene::setVisibleArea(int startRow, int startCol, int visibleRows, int visibleCols)
{
    bool changed = (m_startRow != startRow || m_startCol != startCol ||
                   m_visibleRows != visibleRows || m_visibleCols != visibleCols);
    
    if (changed) {
        m_startRow = startRow;
        m_startCol = startCol;
        m_visibleRows = visibleRows;
        m_visibleCols = visibleCols;
        
        m_needsFullRedraw = true;
        update();
    }
}

void LadderScene::setDebugMode(bool enabled)
{
    if (m_debugMode != enabled) {
        m_debugMode = enabled;
        m_needsFullRedraw = true;
        update();
    }
}

void LadderScene::setEditMode(bool enabled)
{
    if (m_editMode != enabled) {
        m_editMode = enabled;
        m_needsFullRedraw = true;
        update();
    }
}

void LadderScene::setSelection(int row, int col)
{
    if (m_selectedRow != row || m_selectedCol != col) {
        m_selectedRow = row;
        m_selectedCol = col;
        update();
    }
}

void LadderScene::highlightCell(int row, int col, bool highlight)
{
    Q_UNUSED(highlight)
    
    // 更新指定单元格区域
    QRectF cellRect = getCellRect(row, col);
    update(cellRect);
}

void LadderScene::refreshDisplay()
{
    if (!m_forceRefresh) {
        // 检查数据变化
        if (m_ladderData) {
            for (int row = m_startRow; row < m_startRow + m_visibleRows; ++row) {
                if (row >= m_ladderData->getTotalRows()) break;
                
                for (int col = m_startCol; col < m_startCol + m_visibleCols && col < CELL_PER_ROW; ++col) {
                    if (m_ladderData->hasDataChanged(row, col)) {
                        QRectF cellRect = getCellRect(row, col);
                        update(cellRect);
                    }
                }
            }
        }
    } else {
        m_needsFullRedraw = true;
        update();
    }
}

void LadderScene::forceRefresh()
{
    m_forceRefresh = true;
    refreshDisplay();
    m_forceRefresh = false;
}

void LadderScene::drawBackground(QPainter *painter, const QRectF &rect)
{
    QGraphicsScene::drawBackground(painter, rect);
    
    // 绘制网格
    drawGrid(painter, rect);
    
    // 绘制行号
    drawRowNumbers(painter, rect);
    
    // 绘制标题
    drawTitle(painter);
}

void LadderScene::drawForeground(QPainter *painter, const QRectF &rect)
{
    // 绘制梯形图元件
    drawCells(painter, rect);
    
    // 绘制选择框
    drawSelection(painter);
    
    QGraphicsScene::drawForeground(painter, rect);
}

void LadderScene::drawGrid(QPainter *painter, const QRectF &rect)
{
    painter->setPen(QPen(m_gridColor, 1, Qt::SolidLine));
    
    // 绘制垂直网格线
    for (int col = 0; col <= CELL_PER_ROW; ++col) {
        qreal x = LEFT_BLANK + col * CELL_WIDTH;
        if (x >= rect.left() && x <= rect.right()) {
            painter->drawLine(QPointF(x, rect.top()), QPointF(x, rect.bottom()));
        }
    }
    
    // 绘制水平网格线
    int startRow = qMax(0, static_cast<int>((rect.top() - TOP_BLANK) / CELL_HEIGHT));
    int endRow = static_cast<int>((rect.bottom() - TOP_BLANK) / CELL_HEIGHT) + 1;
    
    for (int row = startRow; row <= endRow; ++row) {
        qreal y = TOP_BLANK + row * CELL_HEIGHT;
        if (y >= rect.top() && y <= rect.bottom()) {
            painter->drawLine(QPointF(rect.left(), y), QPointF(rect.right(), y));
        }
    }
}

void LadderScene::drawRowNumbers(QPainter *painter, const QRectF &rect)
{
    painter->setPen(QPen(m_textColor));
    painter->setFont(m_normalFont);
    
    int startRow = qMax(0, static_cast<int>((rect.top() - TOP_BLANK) / CELL_HEIGHT));
    int endRow = static_cast<int>((rect.bottom() - TOP_BLANK) / CELL_HEIGHT) + 1;
    
    for (int row = startRow; row < endRow; ++row) {
        int actualRow = row + m_startRow;
        if (m_ladderData && actualRow >= m_ladderData->getTotalRows()) {
            break;
        }
        
        qreal y = TOP_BLANK + row * CELL_HEIGHT;
        QRectF numberRect(0, y, LEFT_BLANK - 5, CELL_HEIGHT);
        
        QString rowText = QString::number(actualRow);
        painter->drawText(numberRect, Qt::AlignRight | Qt::AlignVCenter, rowText);
    }
}

void LadderScene::drawTitle(QPainter *painter)
{
    painter->setPen(QPen(m_textColor));
    painter->setFont(m_boldFont);
    
    QString title = "PLC梯形图监控";
    if (m_ladderData) {
        title += QString(" - 子程序%1").arg(m_ladderData->getCurrentSubProgram());
    }
    
    QRectF titleRect(0, 0, sceneRect().width(), TOP_BLANK);
    painter->drawText(titleRect, Qt::AlignCenter, title);
}

void LadderScene::drawCells(QPainter *painter, const QRectF &rect)
{
    if (!m_ladderData) return;
    
    // 计算需要绘制的单元格范围
    int startRow = qMax(0, static_cast<int>((rect.top() - TOP_BLANK) / CELL_HEIGHT));
    int endRow = qMin(m_visibleRows, static_cast<int>((rect.bottom() - TOP_BLANK) / CELL_HEIGHT) + 1);
    
    for (int row = startRow; row < endRow; ++row) {
        int actualRow = row + m_startRow;
        if (actualRow >= m_ladderData->getTotalRows()) {
            break;
        }
        
        for (int col = m_startCol; col < m_startCol + m_visibleCols && col < CELL_PER_ROW; ++col) {
            SLadCell cell;
            if (m_ladderData->getLadderCell(actualRow, col, cell)) {
                drawCell(painter, actualRow, col, cell);
            }
        }
    }
}

void LadderScene::drawCell(QPainter *painter, int row, int col, const SLadCell &cell)
{
    QRectF cellRect = getCellRect(row, col);
    CellState state = m_ladderData ? m_ladderData->getCellState(row, col) : CELL_STATE_OFF;
    
    // 根据元件类型绘制
    CellType cellType = getCellType(cell.cmdID);
    
    switch (cellType) {
        case CELL_TYPE_CONTACT:
            drawContact(painter, cellRect, cell, state);
            break;
        case CELL_TYPE_COIL:
            drawCoil(painter, cellRect, cell, state);
            break;
        case CELL_TYPE_FUNCTION:
            drawFunction(painter, cellRect, cell, state);
            break;
        case CELL_TYPE_LINE_H:
        case CELL_TYPE_LINE_V:
            drawLine(painter, cellRect, cell, state);
            break;
        default:
            drawEmpty(painter, cellRect);
            break;
    }
    
    // 绘制边框
    if (cell.rectmask) {
        drawCellBorder(painter, cellRect, cell);
    }
    
    // 绘制参数
    if (cell.parmmask) {
        drawCellParameter(painter, cellRect, cell);
    }
    
    // 绘制注释
    drawCellComment(painter, cellRect, cell);
    
    // 绘制强制状态
    if (m_debugMode && m_ladderData && m_ladderData->isCellForced(row, col)) {
        CellState forceState = m_ladderData->getCellState(row, col);
        drawForceState(painter, cellRect, forceState);
    }
}

void LadderScene::drawContact(QPainter *painter, const QRectF &rect, const SLadCell &cell, CellState state)
{
    QColor color = getCellColor(cell, state);
    painter->setPen(QPen(color, 2));
    
    // 绘制触点符号
    qreal centerX = rect.center().x();
    qreal centerY = rect.center().y();
    qreal width = rect.width() * 0.6;
    qreal height = rect.height() * 0.4;
    
    QRectF contactRect(centerX - width/2, centerY - height/2, width, height);
    
    if (cell.cmdID == I_LD || cell.cmdID == I_AND || cell.cmdID == I_OR) {
        // 常开触点
        painter->drawRect(contactRect);
        painter->drawLine(contactRect.topLeft(), contactRect.bottomRight());
        painter->drawLine(contactRect.topRight(), contactRect.bottomLeft());
    } else {
        // 常闭触点
        painter->drawRect(contactRect);
        painter->drawLine(contactRect.left(), centerY, contactRect.right(), centerY);
    }
    
    // 绘制连接线
    painter->drawLine(rect.left(), centerY, contactRect.left(), centerY);
    painter->drawLine(contactRect.right(), centerY, rect.right(), centerY);
}

void LadderScene::drawCoil(QPainter *painter, const QRectF &rect, const SLadCell &cell, CellState state)
{
    QColor color = getCellColor(cell, state);
    painter->setPen(QPen(color, 2));
    
    // 绘制线圈符号
    qreal centerX = rect.center().x();
    qreal centerY = rect.center().y();
    qreal radius = qMin(rect.width(), rect.height()) * 0.3;
    
    painter->drawEllipse(QPointF(centerX, centerY), radius, radius);
    
    // 根据线圈类型添加标记
    if (cell.cmdID == I_SET) {
        painter->drawText(rect, Qt::AlignCenter, "S");
    } else if (cell.cmdID == I_RST) {
        painter->drawText(rect, Qt::AlignCenter, "R");
    }
    
    // 绘制连接线
    painter->drawLine(rect.left(), centerY, centerX - radius, centerY);
}

void LadderScene::drawFunction(QPainter *painter, const QRectF &rect, const SLadCell &cell, CellState state)
{
    QColor color = getCellColor(cell, state);
    painter->setPen(QPen(color, 2));
    painter->setBrush(QBrush(color.lighter(150)));
    
    // 绘制功能块矩形
    QRectF funcRect = rect.adjusted(5, 5, -5, -5);
    painter->drawRect(funcRect);
    
    // 绘制功能块名称
    painter->setPen(QPen(m_textColor));
    painter->setFont(m_smallFont);
    
    QString funcName;
    switch (cell.cmdID) {
        case I_TMR: funcName = "TMR"; break;
        case I_CTR: funcName = "CTR"; break;
        case I_ADD: funcName = "ADD"; break;
        case I_SUB: funcName = "SUB"; break;
        case I_MUL: funcName = "MUL"; break;
        case I_DIV: funcName = "DIV"; break;
        case I_MOV: funcName = "MOV"; break;
        case I_CMP: funcName = "CMP"; break;
        default: funcName = QString::number(cell.cmdID); break;
    }
    
    painter->drawText(funcRect, Qt::AlignCenter, funcName);
}

void LadderScene::drawLine(QPainter *painter, const QRectF &rect, const SLadCell &cell, CellState state)
{
    QColor color = getCellColor(cell, state);
    painter->setPen(QPen(color, 2));
    
    qreal centerY = rect.center().y();
    
    // 绘制水平连线
    painter->drawLine(rect.left(), centerY, rect.right(), centerY);
    
    // 如果有并联标记，绘制垂直连线
    if (cell.parallel) {
        painter->drawLine(rect.center().x(), rect.top(), rect.center().x(), rect.bottom());
    }
}

void LadderScene::drawEmpty(QPainter *painter, const QRectF &rect)
{
    Q_UNUSED(painter)
    Q_UNUSED(rect)
    // 空元件不绘制任何内容
}

QRectF LadderScene::getCellRect(int row, int col) const
{
    qreal x = LEFT_BLANK + (col - m_startCol) * CELL_WIDTH;
    qreal y = TOP_BLANK + (row - m_startRow) * CELL_HEIGHT;
    return QRectF(x, y, CELL_WIDTH, CELL_HEIGHT);
}

QColor LadderScene::getCellColor(const SLadCell &cell, CellState state, bool selected) const
{
    if (selected) {
        return QColor(0, 0, 255);
    }
    
    if (m_debugMode) {
        switch (state) {
            case CELL_STATE_ON:
                return QColor(255, 0, 0);       // 红色 - 接通
            case CELL_STATE_FORCE_ON:
                return QColor(0, 255, 0);       // 绿色 - 强制接通
            case CELL_STATE_FORCE_OFF:
                return QColor(255, 165, 0);     // 橙色 - 强制断开
            default:
                return QColor(128, 128, 128);   // 灰色 - 断开
        }
    }
    
    return QColor(0, 0, 0); // 默认黑色
}

CellType LadderScene::getCellType(uBit16 cmdID) const
{
    switch (cmdID) {
        case I_LD:
        case I_LDI:
        case I_AND:
        case I_ANI:
        case I_OR:
        case I_ORI:
            return CELL_TYPE_CONTACT;
            
        case I_OUT:
        case I_SET:
        case I_RST:
            return CELL_TYPE_COIL;
            
        case I_TMR:
        case I_CTR:
        case I_ADD:
        case I_SUB:
        case I_MUL:
        case I_DIV:
        case I_MOV:
        case I_CMP:
            return CELL_TYPE_FUNCTION;
            
        case I_LINE:
            return CELL_TYPE_LINE_H;
            
        case I_VOR:
        default:
            return CELL_TYPE_EMPTY;
    }
}

QString LadderScene::getCellParameterText(const SLadCell &cell) const
{
    if (!m_ladderData) return QString();

    // 获取符号名称
    QString symbolName = m_ladderData->getSymbolName(cell.reg);
    if (!symbolName.isEmpty()) {
        return symbolName;
    }

    // 格式化寄存器文本
    QString regText;
    switch (cell.reg.reg_type) {
        case 'X': case 'x':
            regText = QString("X%1").arg(cell.reg.index);
            break;
        case 'Y': case 'y':
            regText = QString("Y%1").arg(cell.reg.index);
            break;
        case 'R': case 'r':
            regText = QString("R%1").arg(cell.reg.index);
            break;
        case 'D': case 'd':
            regText = QString("D%1").arg(cell.reg.index);
            break;
        default:
            regText = QString("%1%2").arg(QChar(cell.reg.reg_type)).arg(cell.reg.index);
            break;
    }

    if (cell.reg.bit >= 0 && cell.reg.bit < 16) {
        regText += QString(".%1").arg(cell.reg.bit);
    }

    return regText;
}

QString LadderScene::getCellCommentText(const SLadCell &cell) const
{
    if (!m_ladderData) return QString();

    return m_ladderData->getSymbolComment(cell.reg);
}

void LadderScene::drawCellBorder(QPainter *painter, const QRectF &rect, const SLadCell &cell)
{
    Q_UNUSED(cell)

    painter->setPen(QPen(m_borderColor, 1, Qt::SolidLine));
    painter->setBrush(Qt::NoBrush);
    painter->drawRect(rect);
}

void LadderScene::drawCellParameter(QPainter *painter, const QRectF &rect, const SLadCell &cell)
{
    QString paramText = getCellParameterText(cell);
    if (paramText.isEmpty()) return;

    painter->setPen(QPen(m_textColor));
    painter->setFont(m_smallFont);

    // 在元件下方绘制参数文本
    QRectF paramRect(rect.left(), rect.bottom() - 15, rect.width(), 15);
    painter->drawText(paramRect, Qt::AlignCenter, paramText);

    // 如果是调试模式，显示当前值
    if (m_debugMode && m_ladderData) {
        quint32 value = m_ladderData->getRegisterValue(cell.reg);
        QString valueText = QString("=%1").arg(value);

        QRectF valueRect(rect.left(), rect.bottom() - 30, rect.width(), 15);
        painter->drawText(valueRect, Qt::AlignCenter, valueText);
    }
}

void LadderScene::drawCellComment(QPainter *painter, const QRectF &rect, const SLadCell &cell)
{
    QString commentText = getCellCommentText(cell);
    if (commentText.isEmpty()) return;

    painter->setPen(QPen(QColor(0, 128, 0))); // 绿色注释
    painter->setFont(m_smallFont);

    // 在元件右侧绘制注释
    QRectF commentRect(rect.right() + 5, rect.top(), 100, rect.height());
    painter->drawText(commentRect, Qt::AlignLeft | Qt::AlignVCenter, commentText);
}

void LadderScene::drawForceState(QPainter *painter, const QRectF &rect, CellState forceState)
{
    QColor forceColor;
    QString forceText;

    switch (forceState) {
        case CELL_STATE_FORCE_ON:
            forceColor = QColor(0, 255, 0, 128);
            forceText = "强制ON";
            break;
        case CELL_STATE_FORCE_OFF:
            forceColor = QColor(255, 165, 0, 128);
            forceText = "强制OFF";
            break;
        default:
            return;
    }

    // 绘制强制状态背景
    painter->setBrush(QBrush(forceColor));
    painter->setPen(Qt::NoPen);
    painter->drawRect(rect.adjusted(2, 2, -2, -2));

    // 绘制强制状态文本
    painter->setPen(QPen(QColor(255, 255, 255)));
    painter->setFont(m_smallFont);
    painter->drawText(rect, Qt::AlignCenter, forceText);
}

void LadderScene::drawSelection(QPainter *painter)
{
    if (m_selectedRow >= 0 && m_selectedCol >= 0) {
        QRectF selectionRect = getCellRect(m_selectedRow, m_selectedCol);

        painter->setPen(QPen(m_selectionColor, 2, Qt::DashLine));
        painter->setBrush(QBrush(m_selectionColor.lighter(180)));
        painter->drawRect(selectionRect);
    }
}

QPointF LadderScene::cellToScene(int row, int col) const
{
    qreal x = LEFT_BLANK + col * CELL_WIDTH + CELL_WIDTH / 2;
    qreal y = TOP_BLANK + (row - m_startRow) * CELL_HEIGHT + CELL_HEIGHT / 2;
    return QPointF(x, y);
}

QPoint LadderScene::sceneToCell(const QPointF &scenePos) const
{
    int col = static_cast<int>((scenePos.x() - LEFT_BLANK) / CELL_WIDTH);
    int row = static_cast<int>((scenePos.y() - TOP_BLANK) / CELL_HEIGHT) + m_startRow;
    return QPoint(col, row);
}

void LadderScene::onDataChanged(int row, int col)
{
    // 标记对应区域需要重绘
    QRectF cellRect = getCellRect(row, col);
    update(cellRect);
}

void LadderScene::onRowDataChanged(int row)
{
    // 标记整行需要重绘
    for (int col = 0; col < CELL_PER_ROW; ++col) {
        onDataChanged(row, col);
    }
}
