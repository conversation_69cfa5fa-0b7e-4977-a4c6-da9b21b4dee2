#-------------------------------------------------
#
# PLC Ladder Diagram Viewer Project
# Based on Qt5 + QGraphicsView
#
#-------------------------------------------------

QT += core gui widgets

TARGET = plc_ladder_viewer
TEMPLATE = app

# C++11 support
CONFIG += c++11

# Include paths
INCLUDEPATH += ../include/api \
               ../include/plc \
               ../include/app \
               ../hncapp/app/src/include

# Source files
SOURCES += \
    main.cpp \
    ladderscene.cpp \
    ladderitem.cpp \
    ladderview.cpp \
    ladderdata.cpp \
    mainwindow.cpp

# Header files
HEADERS += \
    ladderscene.h \
    ladderitem.h \
    ladderview.h \
    ladderdata.h \
    mainwindow.h \
    lad_def_compat.h

# UI files
FORMS += \
    mainwindow.ui

# Resources
RESOURCES += \
    resources.qrc

# Build configuration
CONFIG(debug, debug|release) {
    DESTDIR = debug
    OBJECTS_DIR = debug/obj
    MOC_DIR = debug/moc
    UI_DIR = debug/ui
} else {
    DESTDIR = release
    OBJECTS_DIR = release/obj
    MOC_DIR = release/moc
    UI_DIR = release/ui
}

# Platform specific settings
win32 {
    DEFINES += _WIN32 _MBCS
}

unix:!macx {
    DEFINES += _LINUX_ LINUX
}