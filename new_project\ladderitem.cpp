#include "ladderitem.h"
#include <QGraphicsSceneMouseEvent>
#include <QGraphicsSceneHoverEvent>
#include <QPainterPath>
#include <QFont>
#include <QFontMetrics>
#include <qmath.h>

// 静态颜色定义
const QColor LadderItem::s_defaultColor(0, 0, 0);
const QColor LadderItem::s_activeColor(255, 0, 0);
const QColor LadderItem::s_inactiveColor(128, 128, 128);
const QColor LadderItem::s_selectedColor(0, 0, 255);
const QColor LadderItem::s_forceOnColor(0, 255, 0);
const QColor LadderItem::s_forceOffColor(255, 165, 0);
const QColor LadderItem::s_hoverColor(255, 255, 0, 100);

LadderItem::LadderItem(QGraphicsItem *parent)
    : QGraphicsItem(parent)
    , m_state(CELL_STATE_OFF)
    , m_selected(false)
    , m_forced(false)
    , m_forceState(CELL_STATE_OFF)
    , m_hovered(false)
    , m_debugMode(false)
    , m_boundingRect(0, 0, CELL_WIDTH, CELL_HEIGHT)
{
    // 初始化元件数据
    memset(&m_cell, 0, sizeof(SLadCell));
    
    // 设置项目属性
    setAcceptHoverEvents(true);
    setFlag(QGraphicsItem::ItemIsSelectable, true);
    setFlag(QGraphicsItem::ItemIsFocusable, true);
}

LadderItem::~LadderItem()
{
}

void LadderItem::setCellData(const SLadCell &cell)
{
    if (memcmp(&m_cell, &cell, sizeof(SLadCell)) != 0) {
        m_cell = cell;
        update();
    }
}

void LadderItem::setCellState(CellState state)
{
    if (m_state != state) {
        m_state = state;
        update();
    }
}

void LadderItem::setSelected(bool selected)
{
    if (m_selected != selected) {
        m_selected = selected;
        update();
    }
}

void LadderItem::setForced(bool forced, CellState forceState)
{
    if (m_forced != forced || m_forceState != forceState) {
        m_forced = forced;
        m_forceState = forceState;
        update();
    }
}

void LadderItem::setParameterText(const QString &text)
{
    if (m_parameterText != text) {
        m_parameterText = text;
        update();
    }
}

void LadderItem::setCommentText(const QString &text)
{
    if (m_commentText != text) {
        m_commentText = text;
        update();
    }
}

void LadderItem::setValueText(const QString &text)
{
    if (m_valueText != text) {
        m_valueText = text;
        update();
    }
}

void LadderItem::setDebugMode(bool enabled)
{
    if (m_debugMode != enabled) {
        m_debugMode = enabled;
        update();
    }
}

QRectF LadderItem::boundingRect() const
{
    return m_boundingRect;
}

void LadderItem::paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget)
{
    Q_UNUSED(option)
    Q_UNUSED(widget)
    
    painter->setRenderHint(QPainter::Antialiasing);
    
    QRectF rect = boundingRect();
    QRectF contentRect = getContentRect();
    
    // 绘制悬停效果
    if (m_hovered) {
        painter->setBrush(QBrush(s_hoverColor));
        painter->setPen(Qt::NoPen);
        painter->drawRect(rect);
    }
    
    // 根据元件类型绘制
    CellType cellType = getCellType();
    switch (cellType) {
        case CELL_TYPE_CONTACT:
            drawContact(painter, contentRect);
            break;
        case CELL_TYPE_COIL:
            drawCoil(painter, contentRect);
            break;
        case CELL_TYPE_FUNCTION:
            drawFunction(painter, contentRect);
            break;
        case CELL_TYPE_LINE_H:
        case CELL_TYPE_LINE_V:
            drawLine(painter, contentRect);
            break;
        default:
            drawEmpty(painter, contentRect);
            break;
    }
    
    // 绘制边框
    if (m_cell.rectmask) {
        drawBorder(painter, contentRect);
    }
    
    // 绘制参数
    if (m_cell.parmmask && !m_parameterText.isEmpty()) {
        drawParameter(painter, rect);
    }
    
    // 绘制注释
    if (!m_commentText.isEmpty()) {
        drawComment(painter, rect);
    }
    
    // 绘制值（调试模式）
    if (m_debugMode && !m_valueText.isEmpty()) {
        drawValue(painter, rect);
    }
    
    // 绘制强制状态指示
    if (m_forced) {
        drawForceIndicator(painter, rect);
    }
    
    // 绘制选择框
    if (m_selected) {
        drawSelection(painter, rect);
    }
}

QPainterPath LadderItem::shape() const
{
    QPainterPath path;
    path.addRect(boundingRect());
    return path;
}

bool LadderItem::contains(const QPointF &point) const
{
    return boundingRect().contains(point);
}

void LadderItem::mousePressEvent(QGraphicsSceneMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        setSelected(true);
    }
    QGraphicsItem::mousePressEvent(event);
}

void LadderItem::mouseDoubleClickEvent(QGraphicsSceneMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        // 发送双击信号（需要通过场景传递）
    }
    QGraphicsItem::mouseDoubleClickEvent(event);
}

void LadderItem::hoverEnterEvent(QGraphicsSceneHoverEvent *event)
{
    m_hovered = true;
    update();
    QGraphicsItem::hoverEnterEvent(event);
}

void LadderItem::hoverLeaveEvent(QGraphicsSceneHoverEvent *event)
{
    m_hovered = false;
    update();
    QGraphicsItem::hoverLeaveEvent(event);
}

void LadderItem::drawContact(QPainter *painter, const QRectF &rect)
{
    QColor color = getStateColor();
    painter->setPen(QPen(color, 2));
    painter->setBrush(Qt::NoBrush);
    
    // 计算触点位置
    qreal centerX = rect.center().x();
    qreal centerY = rect.center().y();
    qreal width = rect.width() * 0.6;
    qreal height = rect.height() * 0.4;
    
    QRectF contactRect(centerX - width/2, centerY - height/2, width, height);
    
    // 绘制触点符号
    if (m_cell.cmdID == I_LD || m_cell.cmdID == I_AND || m_cell.cmdID == I_OR) {
        // 常开触点
        painter->drawRect(contactRect);
        painter->drawLine(contactRect.topLeft(), contactRect.bottomRight());
        painter->drawLine(contactRect.topRight(), contactRect.bottomLeft());
    } else {
        // 常闭触点
        painter->drawRect(contactRect);
        painter->drawLine(contactRect.left(), centerY, contactRect.right(), centerY);
    }
    
    // 绘制连接线
    painter->drawLine(rect.left(), centerY, contactRect.left(), centerY);
    painter->drawLine(contactRect.right(), centerY, rect.right(), centerY);
}

void LadderItem::drawCoil(QPainter *painter, const QRectF &rect)
{
    QColor color = getStateColor();
    painter->setPen(QPen(color, 2));
    painter->setBrush(Qt::NoBrush);
    
    // 计算线圈位置
    qreal centerX = rect.center().x();
    qreal centerY = rect.center().y();
    qreal radius = qMin(rect.width(), rect.height()) * 0.3;
    
    // 绘制线圈符号
    painter->drawEllipse(QPointF(centerX, centerY), radius, radius);
    
    // 根据线圈类型添加标记
    painter->setPen(QPen(color));
    painter->setFont(QFont("Arial", 8, QFont::Bold));
    
    if (m_cell.cmdID == I_SET) {
        painter->drawText(rect, Qt::AlignCenter, "S");
    } else if (m_cell.cmdID == I_RST) {
        painter->drawText(rect, Qt::AlignCenter, "R");
    }
    
    // 绘制连接线
    painter->setPen(QPen(color, 2));
    painter->drawLine(rect.left(), centerY, centerX - radius, centerY);
}

void LadderItem::drawFunction(QPainter *painter, const QRectF &rect)
{
    QColor color = getStateColor();
    painter->setPen(QPen(color, 2));
    painter->setBrush(QBrush(color.lighter(180)));
    
    // 绘制功能块矩形
    QRectF funcRect = rect.adjusted(2, 2, -2, -2);
    painter->drawRect(funcRect);
    
    // 绘制功能块名称
    painter->setPen(QPen(QColor(0, 0, 0)));
    painter->setFont(QFont("Arial", 8, QFont::Bold));
    
    QString funcName = getFunctionName();
    painter->drawText(funcRect, Qt::AlignCenter, funcName);
}

void LadderItem::drawLine(QPainter *painter, const QRectF &rect)
{
    QColor color = getStateColor();
    painter->setPen(QPen(color, 2));
    
    qreal centerY = rect.center().y();
    qreal centerX = rect.center().x();
    
    // 绘制水平连线
    painter->drawLine(rect.left(), centerY, rect.right(), centerY);
    
    // 如果有并联标记，绘制垂直连线
    if (m_cell.parallel) {
        painter->drawLine(centerX, rect.top(), centerX, rect.bottom());
    }
}

void LadderItem::drawEmpty(QPainter *painter, const QRectF &rect)
{
    Q_UNUSED(painter)
    Q_UNUSED(rect)
    // 空元件不绘制任何内容
}

void LadderItem::drawBorder(QPainter *painter, const QRectF &rect)
{
    painter->setPen(QPen(QColor(100, 100, 100), 1, Qt::SolidLine));
    painter->setBrush(Qt::NoBrush);
    painter->drawRect(rect);
}

void LadderItem::drawParameter(QPainter *painter, const QRectF &rect)
{
    painter->setPen(QPen(QColor(0, 0, 0)));
    painter->setFont(QFont("Arial", 8));
    
    // 在元件下方绘制参数文本
    QRectF paramRect(rect.left(), rect.bottom() - 15, rect.width(), 15);
    painter->drawText(paramRect, Qt::AlignCenter, m_parameterText);
}

void LadderItem::drawComment(QPainter *painter, const QRectF &rect)
{
    painter->setPen(QPen(QColor(0, 128, 0)));
    painter->setFont(QFont("Arial", 7));
    
    // 在元件右侧绘制注释
    QRectF commentRect(rect.right() + 5, rect.top(), 80, rect.height());
    painter->drawText(commentRect, Qt::AlignLeft | Qt::AlignVCenter, m_commentText);
}

void LadderItem::drawValue(QPainter *painter, const QRectF &rect)
{
    painter->setPen(QPen(QColor(0, 0, 255)));
    painter->setFont(QFont("Arial", 7));
    
    // 在参数下方绘制当前值
    QRectF valueRect(rect.left(), rect.bottom() - 30, rect.width(), 15);
    painter->drawText(valueRect, Qt::AlignCenter, m_valueText);
}

void LadderItem::drawForceIndicator(QPainter *painter, const QRectF &rect)
{
    QColor forceColor;
    QString forceText;
    
    switch (m_forceState) {
        case CELL_STATE_FORCE_ON:
            forceColor = s_forceOnColor;
            forceText = "ON";
            break;
        case CELL_STATE_FORCE_OFF:
            forceColor = s_forceOffColor;
            forceText = "OFF";
            break;
        default:
            return;
    }
    
    // 绘制强制状态背景
    painter->setBrush(QBrush(forceColor.lighter(150)));
    painter->setPen(QPen(forceColor, 1));
    
    QRectF forceRect(rect.right() - 20, rect.top(), 20, 15);
    painter->drawRect(forceRect);
    
    // 绘制强制状态文本
    painter->setPen(QPen(QColor(255, 255, 255)));
    painter->setFont(QFont("Arial", 6, QFont::Bold));
    painter->drawText(forceRect, Qt::AlignCenter, forceText);
}

void LadderItem::drawSelection(QPainter *painter, const QRectF &rect)
{
    painter->setPen(QPen(s_selectedColor, 2, Qt::DashLine));
    painter->setBrush(Qt::NoBrush);
    painter->drawRect(rect);
}

QColor LadderItem::getStateColor() const
{
    if (m_forced) {
        return (m_forceState == CELL_STATE_FORCE_ON) ? s_forceOnColor : s_forceOffColor;
    }
    
    if (m_debugMode) {
        switch (m_state) {
            case CELL_STATE_ON:
                return s_activeColor;
            case CELL_STATE_OFF:
                return s_inactiveColor;
            default:
                return s_defaultColor;
        }
    }
    
    return s_defaultColor;
}

CellType LadderItem::getCellType() const
{
    switch (m_cell.cmdID) {
        case I_LD:
        case I_LDI:
        case I_AND:
        case I_ANI:
        case I_OR:
        case I_ORI:
            return CELL_TYPE_CONTACT;
            
        case I_OUT:
        case I_SET:
        case I_RST:
            return CELL_TYPE_COIL;
            
        case I_TMR:
        case I_CTR:
        case I_ADD:
        case I_SUB:
        case I_MUL:
        case I_DIV:
        case I_MOV:
        case I_CMP:
            return CELL_TYPE_FUNCTION;
            
        case I_LINE:
            return CELL_TYPE_LINE_H;
            
        case I_VOR:
        default:
            return CELL_TYPE_EMPTY;
    }
}

QString LadderItem::getFunctionName() const
{
    switch (m_cell.cmdID) {
        case I_TMR: return "TMR";
        case I_CTR: return "CTR";
        case I_ADD: return "ADD";
        case I_SUB: return "SUB";
        case I_MUL: return "MUL";
        case I_DIV: return "DIV";
        case I_MOV: return "MOV";
        case I_CMP: return "CMP";
        default: return QString::number(m_cell.cmdID);
    }
}

QRectF LadderItem::getContentRect() const
{
    return m_boundingRect.adjusted(1, 1, -1, -1);
}
